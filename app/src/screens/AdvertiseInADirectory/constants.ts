import { ImageSourcePropType } from 'react-native';
import FullPageAdImage from 'src/assets/images/directories/full-page-ad-visual.png';
import BackHalfPageAdImage from 'src/assets/images/directories/back-half-page-ad-visual.png';
import PremiumAdImage from 'src/assets/images/directories/premium-ad-visual.png';
import StandardAdImage from 'src/assets/images/directories/standard-ad-visual.png';

export interface AdType {
  id: string;
  title: string;
  description: string;
  image: ImageSourcePropType;
}

export const AD_TYPES: AdType[] = [
  {
    id: 'full-page-ad',
    title: 'Full page ad',
    description:
      'A full-page ad that lets you show off your work, build your brand, and stand out in style',
    image: FullPageAdImage,
  },
  {
    id: 'back-half-page-ad',
    title: 'Back/Half page ad',
    description:
      'Go bigger with an eye-catching ad space for trades who want to boost awareness and stay front of mind',
    image: BackHalfPageAdImage,
  },
  {
    id: 'premium-ad',
    title: 'Premium ad',
    description:
      'Your listing takes prime position under key headings — ideal for trades ready to stand out in competitive areas',
    image: PremiumAdImage,
  },
  {
    id: 'standard-ad',
    title: 'Standard ad',
    description:
      'A cost-effective way to get your business noticed in local directories',
    image: StandardAdImage,
  },
];
