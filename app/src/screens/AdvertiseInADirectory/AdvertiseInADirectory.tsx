import React, { ReactElement, useEffect } from 'react';
import { View, ScrollView, FlatList } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { Button, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import DirectoryImage from 'src/assets/images/directories/directory.png';
import DirectoryOpenedImage from 'src/assets/images/directories/directory-opened.png';
import { TEL_CROSS_SALES, DIRECTORIES_CALLBACK_FORM_URL } from 'src/constants';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE, ANALYTICS_ACTION_TYPE } from 'src/constants.events';

import { openExternalLink } from 'src/utilities/openExternalLink';
import { AspectRatioImage } from 'src/components/primitives/AspectRatioImage';
import { useMobileMediaQuery } from 'src/hooks/useMediaQuery';
import { ContactType, handlePhoneContact } from 'src/utilities/phone';
import { AD_TYPES, type AdType } from './constants';

const screen = 'advertise_directory';

const TEST_IDS = createTestIds('advertise-in-directory-screen', {
  HERO_IMAGE: 'hero-image',
  CALL_US_BUTTON: 'call-us-button',
  CALL_US_TEXT: 'call-us-text',
  CALLBACK_REQUEST_BUTTON: 'callback-request-button',
  AD_CAROUSEL: 'ad-carousel',
  AD_GRID: 'ad-grid',
  AD_CARD: 'ad-card',
});

// Hero Section Component
const HeroSection = ({ isMobile }: { isMobile: boolean }) => {
  return (
    <LinearGradient
      colors={['#00112C', '#0058A2']}
      start={{ x: 0, y: 1 }}
      end={{ x: 0, y: 0 }}
      style={styles.heroContainer}
    >
      {isMobile ? (
        <AspectRatioImage
          testID={TEST_IDS.HERO_IMAGE}
          source={DirectoryImage}
          dimensionToCalculate="height"
          aspectRatio={1}
          resizeMode="contain"
          style={styles.heroImageMobile}
          accessible
          accessibilityLabel="Trade directory image"
          accessibilityRole="image"
        />
      ) : (
        <View style={styles.heroImagesContainer}>
          <AspectRatioImage
            testID={TEST_IDS.HERO_IMAGE}
            source={DirectoryImage}
            dimensionToCalculate="width"
            aspectRatio={1}
            resizeMode="contain"
            accessible
            accessibilityLabel="Trade directory image"
            accessibilityRole="image"
          />
          <AspectRatioImage
            source={DirectoryOpenedImage}
            dimensionToCalculate="width"
            aspectRatio={1}
            resizeMode="contain"
            accessible
            accessibilityLabel="Opened trade directory image"
            accessibilityRole="image"
          />
        </View>
      )}
    </LinearGradient>
  );
};

// Description Block Component
const DescriptionBlock = ({
  onCallUs,
  onRequestCallback,
}: {
  isMobile: boolean;
  onCallUs: () => void;
  onRequestCallback: () => void;
}) => {
  return (
    <View style={styles.descriptionContainer}>
      <Typography use="subHeader" style={styles.title}>
        {'Advertise in a directory'}
      </Typography>
      <Typography use="bodyRegular" style={styles.description}>
        {'Our Checkatrade directories can help you to grow your '}
        {'business by putting your trade in front of a '}
        {'whole new audience in the areas you want to work.'}
      </Typography>
      <Button
        block
        variant="secondary"
        label="Call us"
        onPress={onCallUs}
        testID={TEST_IDS.CALL_US_BUTTON}
        style={styles.buttonSpacing}
      />
      <Button
        block
        variant="tertiary"
        label="Book a call back"
        onPress={onRequestCallback}
        testID={TEST_IDS.CALLBACK_REQUEST_BUTTON}
      />
    </View>
  );
};

// Marketing Details Section Component
const MarketingDetailsSection = () => {
  return (
    <View style={styles.marketingContainer}>
      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'Get more leads'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'Directories are an effective way of reaching a valuable '}
          {'audience, with the product delivered directly through a '}
          {"customers' letterbox."}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionTextLast}>
          {'Directories not only drive immediate responses but also '}
          {'create lasting halo effects, enhancing brand awareness and '}
          {'customer retention.'}
        </Typography>
      </View>

      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'Beat the competition'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'We have a limit for each trade heading, which means '}
          {"you'll be competing with fewer trades within the area."}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionTextLast}>
          {'Select for our range of advertisement options to showcase '}
          {'your business and be top of mind when a customer needs you.'}
        </Typography>
      </View>

      <View style={styles.marketingSection}>
        <Typography use="subHeader" style={styles.sectionTitle}>
          {'26 years of success'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'With '}
          <Typography use="bodyBold">{'26 years of service'}</Typography>
          {
            ', these directories cover 358 local areas, reaching an average of 70,000 households per area.'
          }
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionText}>
          {'They are circulated bi-monthly, with '}
          <Typography use="bodyBold">{'23.6 million copies '}</Typography>
          {'distributed per drop.'}
        </Typography>
        <Typography use="bodyRegular" style={styles.sectionTextLast}>
          {'With an '}
          <Typography use="bodyBold">{'80% readership '}</Typography>
          {'among the households they reach, they have a '}
          <Typography use="bodyBold">{'40% longer shelf life '}</Typography>
          {
            'in homes compared to other forms of print, demonstrating their lasting value and trusted reputation.'
          }
        </Typography>
      </View>
    </View>
  );
};

// Ad Type Card Component
const AdTypeCard = ({ adType }: { adType: AdType }) => {
  return (
    <View style={styles.adCard}>
      <AspectRatioImage
        style={styles.adCardImage}
        source={adType.image}
        dimensionToCalculate="height"
        aspectRatio={1}
        resizeMode="cover"
        accessible
        accessibilityLabel={`${adType.title} advertisement example`}
        accessibilityRole="image"
      />
      <View style={styles.adCardContent}>
        <Typography use="subHeader" style={styles.adCardTitle}>
          {adType.title}
        </Typography>
        <Typography use="bodyRegular" style={styles.adCardDescription}>
          {adType.description}
        </Typography>
      </View>
    </View>
  );
};

// Ad Types Carousel Component (Mobile)
const AdTypesCarousel = () => {
  const renderAdType = ({ item }: { item: AdType }) => (
    <AdTypeCard adType={item} />
  );

  return (
    <View style={styles.carouselContainer}>
      <Typography use="subHeader" style={styles.sectionTitle}>
        {'Choose the right advert for your business needs'}
      </Typography>
      <FlatList
        testID={TEST_IDS.AD_CAROUSEL}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.carouselContent}
        ItemSeparatorComponent={() => <View style={styles.carouselSeparator} />}
      />
    </View>
  );
};

// Ad Types Grid Component (Web)
const AdTypesGrid = () => {
  const renderAdType = ({ item }: { item: AdType }) => (
    <View style={styles.gridItem}>
      <AdTypeCard adType={item} />
    </View>
  );

  return (
    <View style={styles.gridContainer}>
      <Typography use="subHeader" style={styles.sectionTitle}>
        {'Choose the right advert for your business needs'}
      </Typography>
      <FlatList
        testID={TEST_IDS.AD_GRID}
        data={AD_TYPES}
        renderItem={renderAdType}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.gridContent}
        columnWrapperStyle={styles.gridRow}
      />
    </View>
  );
};

export function AdvertiseInADirectory(): ReactElement {
  const isMobile = useMobileMediaQuery();

  const handleCallUs = () => {
    logEvent(EVENT_TYPE.DIRECTORIES_CALL_US);
    handlePhoneContact(ContactType.call, TEL_CROSS_SALES);
  };

  const handleRequestCallback = () => {
    logEvent(EVENT_TYPE.DIRECTORIES_REQUEST_CALLBACK);
    openExternalLink(DIRECTORIES_CALLBACK_FORM_URL);
  };

  useEffect(() => {
    logEvent(`${screen}_${ANALYTICS_ACTION_TYPE.VIEWED}`);
  }, []);

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.container} testID={TEST_IDS.ROOT}>
        <HeroSection isMobile={isMobile} />
        <DescriptionBlock
          isMobile={isMobile}
          onCallUs={handleCallUs}
          onRequestCallback={handleRequestCallback}
        />
        <MarketingDetailsSection />
        {isMobile ? <AdTypesCarousel /> : <AdTypesGrid />}
      </View>
    </ScrollView>
  );
}

AdvertiseInADirectory.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: palette.mortarV3.tokenDefault100,
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    justifyContent: 'space-between',
  },

  // Hero Section Styles
  heroContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300,
  },
  heroImagesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  },
  heroImageMobile: {
    maxWidth: 280,
    width: '100%',
  },

  // Description Block Styles
  descriptionContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  title: {
    marginBottom: spacing(2),
    textAlign: 'center',
  },
  description: {
    marginBottom: spacing(3),
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonSpacing: {
    marginBottom: spacing(2),
  },

  // Marketing Section Styles
  marketingContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  marketingSection: {
    marginBottom: spacing(4),
  },
  sectionTitle: {
    marginBottom: spacing(2),
    fontWeight: '600',
  },
  sectionText: {
    marginBottom: spacing(2),
    lineHeight: 22,
  },
  sectionTextLast: {
    lineHeight: 22,
  },

  // Ad Types Carousel Styles (Mobile)
  carouselContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  carouselContent: {
    paddingHorizontal: spacing(1),
  },
  carouselSeparator: {
    width: spacing(2),
  },

  // Ad Types Grid Styles (Web)
  gridContainer: {
    padding: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  gridContent: {
    gap: spacing(2),
  },
  gridRow: {
    justifyContent: 'space-between',
    gap: spacing(2),
  },
  gridItem: {
    flex: 1,
    maxWidth: '48%',
  },

  // Ad Card Styles
  adCard: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
    borderRadius: 8,
    shadowColor: palette.mortar.tokenColorBlack,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    minWidth: 280,
  },
  adCardImage: {
    width: '100%',
    height: 160,
  },
  adCardContent: {
    padding: spacing(2),
  },
  adCardTitle: {
    marginBottom: spacing(1),
    fontWeight: '600',
  },
  adCardDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: palette.mortar.tokenColorDarkGrey,
  },
}));
